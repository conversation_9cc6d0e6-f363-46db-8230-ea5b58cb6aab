= Rhubarb for After Effects
:experimental:
:imagesdir: ../../img
:A: &#9398;
:B: &#9399;
:C: &#9400;
:D: &#9401;
:E: &#9402;
:F: &#9403;
:G: &#9404;
:H: &#9405;
:X: &#9421;

The script in this directory generates After Effects compositions with mouth animation.

== How to install

=== 1. Download and extract

Download the archive file containing Rhubarb Lip Sync, then extract in a directory on your computer.

=== 2. Add Rhubarb to the PATH environment variable

After Effects needs to know where to find Rhubarb Lip Sync.
So you need to add the Rhubarb directory to your computer's PATH environment variable as follows:

*On Windows:*

. *Copy the full path of the Rhubarb directory:*
* In the File Explorer, press and hold the kbd:[Shift] key while right-clicking on the Rhubarb directory (the directory containing `rhubarb`, `README.adoc`, etc.).
* In the context menu, select _Copy as path_.

. *Add the Rhubarb directory to the PATH variable:*
* Press kbd:[Start] + kbd:[R] to open the Run dialog.
* Type `sysdm.cpl` and press kbd:[Enter] to open the System Properties window.
* In the System Properties window, go to the _Advanced_ tab and click the _Environment Variables..._ button.
* In the _Environment Variables_ window, under _System variables_, select the _Path_ variable, then click _Edit..._.
* In the Edit Environment Variable window, click _New_, then paste the path you copied earlier.
* Click _OK_ to close all windows.
* To make sure everything is working, open a new Command Prompt window by pressing kbd:[Start] + kbd:[R], typing `cmd`, and pressing kbd:[Enter]. Type `rhubarb --version` and press kbd:[Enter]. You should see the version number of Rhubarb Lip Sync.

*On macOS:*

. *Copy the full path of the Rhubarb directory:*
* In the Finder app, right-click on the Rhubarb directory (the directory containing `rhubarb`, `README.adoc`, etc.).
* While the context menu is open, press and hold the kbd:[Option] key. This will change some of the menu items.
* Select _Copy [...] as Pathname_.

. *Open the Terminal app:*
* Click on the Spotlight search icon (the little magnifying glass) at the top-right of your screen.
* Into the search bar that opens, type "`terminal`", then press kbd:[Enter].

. *Add the Rhubarb directory to the PATH variable:*
* In the Terminal window, type `touch ~/.zshrc` and confirm with kbd:[Enter].
* Next, type `open -e ~/.zshrc` and confirm with kbd:[Enter]. The TextEdit app should open with a file.
* If the file already contains text, move to the end of the file and add a new line.
* Type `export PATH="`, press kbd:[Cmd] + kbd:[V] to paste the path you copied earlier, then type `:$PATH"`. +
The resulting line should look something like `export PATH="/Users/<USER>/Rhubarb-Lip-Sync:$PATH"`.
* Save the file and close TextEdit.
* To make sure everything is working, close the terminal window, then open it again as described above. Type `rhubarb --version` and confirm with kbd:[Enter]. You should see the version number of Rhubarb Lip Sync.

=== 3. Install After Effects script

In After Effects, go to _File_ > _Scripts_ > _Install Script File..._, then select the script file `Rhubarb Lip Sync.jsx` from the `extras/AdobeAfterEffects` subdirectory of Rhubarb Lip Sync.

Older After Effects versions don't have this command, so you'll have to manually copy (or symlink) the script file into your After Effects scripts directory.

* On *Windows*, that directory is usually `C:\Program Files\Adobe\Adobe After Effects <version>\Support Files\Scripts`.
* On *OS X*, that directory is usually `Applications/Adobe After Effects <version>/Scripts`.

=== 4. (Re-)start After Effects

== How to use

=== Create your mouth composition

To start, you need a composition containing all your mouth shapes.
The first frame (`00:00`) should contain the {A} mouth shape, the second frame (`00:01`) should contain the {B} mouth shape, and so on.
The optional mouth shapes {G}, {H}, and {X} belong at `00:06`, `00:07`, and `00:08`, respectively.

For your first experiments, check out the demo project in the `extras/AdobeAfterEffects/demo` directory.

image:after-effects-setup.png[]

=== Run Rhubarb for After Effects

In After Effects, select _File > Scripts > Rhubarb Lip Sync.jsx_. That will open a dialog window where you can specify the audio file with the dialog recording and a number of other options. To get information about any input field, just hover above it with your mouse and you'll see a tooltip.
