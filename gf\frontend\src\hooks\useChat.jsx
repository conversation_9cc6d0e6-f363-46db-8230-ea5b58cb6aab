import { createContext, useContext, useEffect, useMemo, useState } from "react";

const backendUrl = import.meta.env.VITE_API_URL || "http://localhost:3000";

const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState();
  const [loading, setLoading] = useState(false);
  const [cameraZoomed, setCameraZoomed] = useState(true);

  // NEW: basic error + rate-limit flags to satisfy UI.jsx
  const [error, setError] = useState(null);
  const [rateLimited, setRateLimited] = useState(false);

  const clearError = () => setError(null);

  const onMessagePlayed = () => {
    setMessages((messages) => messages.slice(1));
  };

  useEffect(() => {
    if (messages.length > 0) {
      setMessage(messages[0]);
    } else {
      setMessage(null);
    }
  }, [messages]);

  const chat = async (userText) => {
    if (!userText) return;
    setLoading(true);
    setError(null);
    try {
      const data = await fetch(`${backendUrl}/chat`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: userText })
      });

      // rate limit / server errors
      if (data.status === 429) {
        setRateLimited(true);
        setError({ type: 'rate_limit_server', message: 'Rate limit reached. Please try again shortly.', isTemporary: true, retryAfter: 10 });
        setTimeout(() => setRateLimited(false), 10000);
        setLoading(false);
        return;
      }
      if (!data.ok) {
        const txt = await data.text();
        throw new Error(txt || `HTTP ${data.status}`);
      }

      const resp = (await data.json()).messages;
      if (!Array.isArray(resp)) throw new Error('Backend response missing messages[]');
      setMessages((messages) => [...messages, ...resp]);
    } catch (e) {
      setError({ type: 'network_error', message: e.message || 'Network error', isTemporary: true });
    } finally {
      setLoading(false);
    }
  };

  const value = useMemo(() => ({
    chat,
    message,
    onMessagePlayed,
    loading,
    cameraZoomed,
    setCameraZoomed,
    // error ui fields
    error,
    clearError,
    rateLimited,
  }), [message, loading, cameraZoomed, error, rateLimited]);

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) throw new Error("useChat must be used within a ChatProvider");
  return context;
};