import { exec } from "child_process";
import cors from "cors";
import dotenv from "dotenv";
import voice from "elevenlabs-node";
import express from "express";
import { promises as fs } from "fs";
import { GoogleGenerativeAI } from "@google/generative-ai";

dotenv.config();

// ✅ Correct SDK init
const genAI = new GoogleGenerativeAI({ apiKey: process.env.GEMINI_API_KEY });
const elevenLabsApiKey = process.env.ELEVEN_LABS_API_KEY;
const voiceID = process.env.ELEVEN_VOICE_ID || "21m00Tcm4TlvDq8ikWAM"; // default voice if missing

const app = express();
app.use(express.json({ limit: '10mb' }));
app.use(cors());
const port = process.env.PORT || 3000;

app.get("/", (req, res) => {
  res.send("Backend OK");
});

app.get("/voices", async (req, res) => {
  try {
    res.send(await voice.getVoices(elevenLabsApiKey));
  } catch (e) {
    res.status(500).send({ error: e.message });
  }
});

// ---------- HELPERS ----------
const execCommand = (command) => new Promise((resolve, reject) => {
  exec(command, (error, stdout, stderr) => {
    if (error) return reject(error);
    resolve(stdout);
  });
});

const ensureDir = async (p) => { try { await fs.mkdir(p, { recursive: true }); } catch(_){} }

const lipSyncMessage = async (i) => {
  await execCommand(`ffmpeg -y -i audios/message_${i}.mp3 audios/message_${i}.wav`);
  await execCommand(`./bin/rhubarb -f json -o audios/message_${i}.json audios/message_${i}.wav -r phonetic`);
};

const readJson = async (file) => JSON.parse(await fs.readFile(file, "utf8"));
const fileToBase64 = async (file) => (await fs.readFile(file)).toString("base64");

// Gemini system prompt builder
const buildPrompt = (userMessage) => `
You are a virtual Teacher.
Return ONLY valid JSON with this exact shape:
{
  "messages": [
    {"text": string, "facialExpression": "smile|sad|angry|surprised|laugh|default", "animation": string},
    {"text": string, "facialExpression": "smile|sad|angry|surprised|laugh|default", "animation": string}
  ]
}
- Max 3 items.
- animations must be one of the actual clip names in the avatar (e.g., Idle, Talking, Walk, etc.).
User: ${userMessage}
`;

app.post("/chat", async (req, res) => {
  const userMessage = (req.body?.message || "").toString();
  await ensureDir('audios');

  if (!userMessage) {
    // Default intros (use your existing files)
    const out = {
      messages: [
        {
          text: "Hey dear... How was your day?",
          audio: await fileToBase64("audios/intro_0.wav"),
          lipsync: await readJson("audios/intro_0.json"),
          facialExpression: "smile",
          animation: "Talking_1",
        },
        {
          text: "I missed you so much... Please don't go for so long!",
          audio: await fileToBase64("audios/intro_1.wav"),
          lipsync: await readJson("audios/intro_1.json"),
          facialExpression: "sad",
          animation: "Crying",
        },
      ],
    };
    return res.send(out);
  }

  if (!elevenLabsApiKey || !process.env.GEMINI_API_KEY) {
    const out = {
      messages: [
        {
          text: "Please my dear, don't forget to add your API keys!",
          audio: await fileToBase64("audios/api_0.wav"),
          lipsync: await readJson("audios/hi.json"),
          facialExpression: "angry",
          animation: "Angry",
        },
        {
          text: "You don't want to ruin Wawa Sensei with a crazy bill, right?",
          audio: await fileToBase64("audios/api_1.wav"),
          lipsync: await readJson("audios/api_1.json"),
          facialExpression: "smile",
          animation: "Laughing",
        },
      ],
    };
    return res.send(out);
  }

  try {
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    const result = await model.generateContent(buildPrompt(userMessage));
    const txt = (result?.response?.text?.() || '').trim();

    // Robust JSON parse (strip code fences if any)
    const jsonStr = txt.replace(/^```json/,'').replace(/```$/,'').trim();
    let parsed;
    try { parsed = JSON.parse(jsonStr) } catch(e) {
      // fallback minimal schema if LLM returned plain text
      parsed = { messages: [{ text: txt.slice(0, 200), facialExpression: 'default', animation: 'Idle' }] }
    }

    let messages = parsed.messages || [];
    if (!Array.isArray(messages) || messages.length === 0) {
      messages = [{ text: 'Sorry, I had trouble understanding that.', facialExpression: 'default', animation: 'Idle' }]
    }

    // Synthesize audio + rhubarb for each message
    for (let i = 0; i < messages.length; i++) {
      const m = messages[i]
      const text = (m.text || '').toString()
      const mp3File = `audios/message_${i}.mp3`

      await voice.textToSpeech(elevenLabsApiKey, voiceID, mp3File, text)
      await lipSyncMessage(i)

      m.audio = await fileToBase64(mp3File)
      m.lipsync = await readJson(`audios/message_${i}.json`)
      m.facialExpression = m.facialExpression || 'default'
      m.animation = m.animation || 'Idle'
    }

    res.send({ messages })
  } catch (error) {
    console.error('Error in /chat:', error)
    res.status(500).send({ error: 'Processing failed', details: error.message })
  }
})

app.listen(port, () => {
  console.log(`Backend listening on :${port}`)
  console.log("- Gemini API Key:", process.env.GEMINI_API_KEY ? "✓ Loaded" : "✗ Missing")
  console.log("- ElevenLabs API Key:", elevenLabsApiKey ? "✓ Loaded" : "✗ Missing")
})