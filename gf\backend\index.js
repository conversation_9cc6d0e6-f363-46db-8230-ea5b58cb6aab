import { exec } from "child_process";
import cors from "cors";
import dotenv from "dotenv";
import voice from "elevenlabs-node";
import express from "express";
import { promises as fs } from "fs";
import { GoogleGenerativeAI } from "@google/generative-ai"; // ✅ Gemini import

dotenv.config();

const genAI = new GoogleGenerativeAI({ apiKey: process.env.GEMINI_API_KEY || "-" }); // ✅ FIX: apiKey (not apikey)
const elevenLabsApiKey = process.env.ELEVEN_LABS_API_KEY;
const voiceID = "5sVY35LYBzCqRl4zQcf6";

const app = express();
app.use(express.json());
app.use(cors());
const port = 3000;

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.get("/voices", async (req, res) => {
  res.send(await voice.getVoices(elevenLabsApiKey));
});

// ---------- HELPERS ----------
const execCommand = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) reject(error);
      resolve(stdout);
    });
  });
};

const lipSyncMessage = async (message) => {
  await execCommand(
    `ffmpeg -y -i audios/message_${message}.mp3 audios/message_${message}.wav`
  );
  await execCommand(
    `./bin/rhubarb -f json -o audios/message_${message}.json audios/message_${message}.wav -r phonetic`
  );
};

const readJsonTranscript = async (file) => {
  const data = await fs.readFile(file, "utf8");
  return JSON.parse(data);
};

const audioFileToBase64 = async (file) => {
  const data = await fs.readFile(file);
  return data.toString("base64");
};

// ---------------- CHAT ROUTE ----------------
app.post("/chat", async (req, res) => {
  const userMessage = req.body.message;

  // No input → default intro messages
  if (!userMessage) {
    res.send({
      messages: [
        {
          text: "Hey dear... How was your day?",
          audio: await audioFileToBase64("audios/intro_0.wav"),
          lipsync: await readJsonTranscript("audios/intro_0.json"),
          facialExpression: "smile",
          animation: "Talking_1",
        },
        {
          text: "I missed you so much... Please don't go for so long!",
          audio: await audioFileToBase64("audios/intro_1.wav"),
          lipsync: await readJsonTranscript("audios/intro_1.json"),
          facialExpression: "sad",
          animation: "Crying",
        },
      ],
    });
    return;
  }

  // Missing API keys → warning
  if (!elevenLabsApiKey || genAI.apiKey === "-") {
    res.send({
      messages: [
        {
          text: "Please my dear, don't forget to add your API keys!",
          audio: await audioFileToBase64("audios/api_0.wav"),
          lipsync: await readJsonTranscript("audios/hi.json"),
          facialExpression: "angry",
          animation: "Angry",
        },
        {
          text: "You don't want to ruin Wawa Sensei with a crazy bill, right?",
          audio: await audioFileToBase64(
            "ElevenLabs_2025-08-22T16_42_19_Drew_pre_sp100_s50_sb75_se0_b_m2.ogg"
          ),
          lipsync: await readJsonTranscript("audios/api_1.json"),
          facialExpression: "smile",
          animation: "Laughing",
        },
      ],
    });
    return;
  }

  // ✅ Main AI logic
  try {
    console.log("Making Gemini API call...");
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const prompt = `
      You are a virtual Teacher. 
      Always reply with a JSON array called "messages" (max 3 items).
      Each message has: text, facialExpression, animation.
      facialExpression options: smile, sad, angry, surprised, laugh, default.
      animation options: Armature.001|mixamo.com|Layer0, tak2, taking, taking dif, talk222, talking, talking.001, thinking, walk.
      User: ${userMessage}
    `;

    const result = await model.generateContent(prompt);
    const textResponse = result.response.text();

    console.log("Gemini API response received");
    console.log("Raw response:", textResponse);

    let messages = JSON.parse(textResponse);
    if (messages.messages) {
      messages = messages.messages;
    }

    console.log("Parsed messages:", messages);

    // Generate voice + lipsync for each
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      console.log(`Processing message ${i}:`, message.text);

      const fileName = `audios/message_${i}.mp3`;
      await voice.textToSpeech(elevenLabsApiKey, voiceID, fileName, message.text);
      await lipSyncMessage(i);

      message.audio = await audioFileToBase64(fileName);
      message.lipsync = await readJsonTranscript(`audios/message_${i}.json`);
    }

    console.log("Sending response with", messages.length, "messages");
    res.send({ messages });
  } catch (error) {
    console.error("Error in chat endpoint:", error);
    res.status(500).send({
      error: "An error occurred while processing your request",
      details: error.message,
    });
  }
});

// ---------------- SERVER START ----------------
app.listen(port, () => {
  console.log(`Virtual Girlfriend listening on port ${port}`);
  console.log("Environment check:");
  console.log(
    "- Gemini API Key:",
    process.env.GEMINI_API_KEY ? "✓ Loaded" : "✗ Missing"
  );
  console.log(
    "- ElevenLabs API Key:",
    process.env.ELEVEN_LABS_API_KEY ? "✓ Loaded" : "✗ Missing"
  );
});
