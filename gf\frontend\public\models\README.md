# 3D Avatar Models

This directory should contain the 3D model files for the Virtual Girlfriend application.

## Required Files

The application expects the following files to be placed in this directory:

1. **avatar.glb** - The main 3D character model with rigging and morph targets
2. **animations.glb** - Animation clips for the character (idle, talking, etc.)

## Where to Get the Models

These model files are not included in the repository due to size constraints. You can obtain them by:

### Option 1: Ready Player Me (Recommended)
1. Visit [Ready Player Me](https://readyplayer.me/)
2. Create a custom avatar
3. Download the GLB file and rename it to `avatar.glb`
4. For animations, you can use Mixamo or create your own

### Option 2: Mixamo
1. Visit [Adobe Mixamo](https://www.mixamo.com/)
2. Upload your character model or use one of their characters
3. Download animations and combine them into `animations.glb`

### Option 3: Custom Models
- Create your own models using Blender, Maya, or other 3D software
- Ensure the model has proper rigging and morph targets for facial expressions
- Export as GLB format

## Model Requirements

For the avatar to work properly, the model should have:

- **Rigging**: Proper bone structure for animations
- **Morph Targets**: Facial blend shapes for lip sync and expressions
  - viseme_aa, viseme_ee, viseme_ih, viseme_oh, viseme_ou
  - viseme_pp, viseme_ff, viseme_th, viseme_s, viseme_sh
  - viseme_rr, viseme_dd, viseme_kk, viseme_nn
  - jawOpen, mouthSmileLeft, mouthSmileRight, etc.
- **Materials**: Properly named materials (Wolf3D_Skin, Wolf3D_Eye, etc.)
- **Mesh Names**: Expected mesh names like Wolf3D_Head, Wolf3D_Body, etc.

## Fallback Behavior

If the model files are missing, the application will display a simple placeholder (red cube with sphere head) instead of crashing.

## File Size Considerations

GLB files can be large (10-50MB+). Consider:
- Optimizing textures and geometry
- Using compression tools like gltf-pipeline
- Hosting files externally if needed

## Troubleshooting

If you're getting 404 errors:
1. Ensure files are named exactly `avatar.glb` and `animations.glb`
2. Check that files are in the `public/models/` directory
3. Restart the development server after adding files
4. Check browser console for specific error messages
