import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useAnimations } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'
import { useChat } from '../hooks/useChat'

// Rhubarb letters -> RPM visemes (safe defaults). Adjust if your avatar has different morphs
const RHUBARB_TO_RPM = {
  A: ['viseme_aa', 'jawOpen'], // open wide
  B: ['viseme_pp'],            // closed lips (BMP)
  C: ['viseme_ee', 'viseme_ih'], // wide smile / "ee"
  D: ['viseme_aa', 'viseme_dd'],
  E: ['viseme_oh'],            // round "oh"
  F: ['viseme_ou', 'viseme_ff'], // "oo" / teeth
  G: ['viseme_ff', 'viseme_s'],  // teeth / fricatives
  H: ['viseme_th'],
  X: [] // silence
}

// fallback facial expressions (tweak to taste)
const facialExpressions = {
  default: {},
  smile: { mouthSmileLeft: 0.7, mouthSmileRight: 0.7 },
  sad: { mouthFrownLeft: 0.7, mouthFrownRight: 0.7 },
  angry: { browDownLeft: 0.5, browDownRight: 0.5 },
  surprised: { eyeWideLeft: 0.5, eyeWideRight: 0.5, jawOpen: 0.3 },
  laugh: { mouthUpperUpLeft: 0.4, mouthUpperUpRight: 0.5, mouthSmileLeft: 1, mouthSmileRight: 1 },
}

// WebAudio helper
const base64ToArrayBuffer = (b64) => {
  const binary_string = atob(b64);
  const len = binary_string.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) bytes[i] = binary_string.charCodeAt(i);
  return bytes.buffer;
}

export function Avatar(props) {
  const group = useRef()

  // Try to load models, but provide fallbacks if they don't exist
  let nodes = {}, animations = []

  // For now, let's comment out the model loading to prevent 404 errors
  // const { nodes, materials } = useGLTF('/models/avatar.glb')
  // const { animations } = useGLTF('/models/animations.glb')

  const { actions, mixer } = useAnimations(animations, group)

  const { message, onMessagePlayed, chat } = useChat()

  // state
  const [currentAnim, setCurrentAnim] = useState('Idle')
  const [facial, setFacial] = useState('default')
  const [lipsync, setLipsync] = useState(null)

  // WebAudio state
  const audioCtxRef = useRef(null)
  const audioSourceRef = useRef(null)
  const audioStartTimeRef = useRef(0)
  const audioDurationRef = useRef(0)
  const analyserRef = useRef(null) // for amplitude fallback

  const headMesh = useMemo(() => (nodes.Wolf3D_Head || nodes.Head || nodes.Wolf3D_Avatar), [nodes])

  // safely set morph
  const setMorph = (name, value) => {
    if (!headMesh?.morphTargetDictionary) return
    const idx = headMesh.morphTargetDictionary[name]
    if (idx !== undefined && headMesh.morphTargetInfluences[idx] !== undefined) {
      headMesh.morphTargetInfluences[idx] = value
    }
  }

  const lerpMorph = (name, value, spd=0.2) => {
    if (!headMesh?.morphTargetDictionary) return
    const idx = headMesh.morphTargetDictionary[name]
    if (idx !== undefined && headMesh.morphTargetInfluences[idx] !== undefined) {
      headMesh.morphTargetInfluences[idx] = THREE.MathUtils.lerp(
        headMesh.morphTargetInfluences[idx], value, spd
      )
    }
  }

  // Play animation safely
  useEffect(() => {
    if (!animations?.length || !actions) return
    const desired = currentAnim
    const available = Object.keys(actions)
    let chosen = desired

    if (!available.includes(desired)) {
      // try to find a close match, else fallback to first
      const talk = available.find(n => /talk/i.test(n))
      const idle = available.find(n => /idle/i.test(n))
      chosen = talk || idle || available[0]
      console.warn('Requested animation not found:', desired, '→ using', chosen)
    }

    const action = actions[chosen]
    if (!action) return

    // strip problematic tracks that point to missing bones
    if (action._clip?.tracks?.length) {
      const validTracks = action._clip.tracks.filter(track => {
        const name = track.name?.split('.')[0]
        return !!group.current?.getObjectByName(name)
      })
      action._clip.tracks = validTracks
    }

    action.reset().fadeIn(mixer?.stats?.actions?.inUse === 0 ? 0 : 0.3).play()
    return () => { try { action.fadeOut(0.2) } catch(e){} }
  }, [currentAnim, actions, animations, mixer])

  // When a new message arrives from backend
  useEffect(() => {
    if (!message) {
      setCurrentAnim('Idle')
      return
    }
    setCurrentAnim(message.animation || 'Idle')
    setFacial(message.facialExpression || 'default')
    setLipsync(message.lipsync || null)

    // Stop previous audio if any
    try { audioSourceRef.current?.stop?.() } catch (e) {}

    // WebAudio play
    const run = async () => {
      const ctx = audioCtxRef.current || new (window.AudioContext || window.webkitAudioContext)()
      audioCtxRef.current = ctx
      const arr = base64ToArrayBuffer(message.audio)
      const buf = await ctx.decodeAudioData(arr.slice(0))
      const src = ctx.createBufferSource()
      src.buffer = buf
      const gain = ctx.createGain()
      src.connect(gain)
      gain.connect(ctx.destination)

      // analyser for amplitude fallback
      const analyser = ctx.createAnalyser()
      analyser.fftSize = 2048
      gain.connect(analyser)
      analyserRef.current = analyser

      const startAt = ctx.currentTime + 0.05
      src.start(startAt)

      audioSourceRef.current = src
      audioStartTimeRef.current = startAt
      audioDurationRef.current = buf.duration

      src.onended = () => {
        setCurrentAnim('Idle')
        onMessagePlayed?.()
      }
    }
    run()
  }, [message])

  // facial expression each frame
  useFrame(() => {
    const mapping = facialExpressions[facial] || {}
    // reset all known keys we care about subtly
    ;['mouthSmileLeft','mouthSmileRight','mouthFrownLeft','mouthFrownRight','browDownLeft','browDownRight','eyeWideLeft','eyeWideRight','jawOpen']
      .forEach(k => lerpMorph(k, mapping[k] || 0, 0.15))
  })

  // lipsync each frame (viseme schedule or amplitude fallback)
  useFrame(() => {
    if (!audioCtxRef.current) return

    const now = audioCtxRef.current.currentTime
    const t = now - audioStartTimeRef.current

    // Zero-out visemes this frame first
    const allVisemes = new Set([ 'jawOpen',
      'viseme_aa','viseme_ee','viseme_ih','viseme_oh','viseme_ou','viseme_pp','viseme_ff','viseme_th','viseme_s','viseme_sh','viseme_rr','viseme_dd','viseme_kk','viseme_nn'
    ])
    allVisemes.forEach(v => lerpMorph(v, 0, 0.25))

    let droveFromCues = false
    if (lipsync?.mouthCues?.length) {
      const cue = lipsync.mouthCues.find(c => t >= c.start && t <= c.end)
      if (cue) {
        droveFromCues = true
        const targets = RHUBARB_TO_RPM[cue.value] || []
        targets.forEach(v => lerpMorph(v, 1, 0.25))
      }
    }

    // amplitude fallback when no cue is active
    if (!droveFromCues && analyserRef.current) {
      const analyser = analyserRef.current
      const data = new Uint8Array(analyser.frequencyBinCount)
      analyser.getByteTimeDomainData(data)
      let sum=0
      for (let i=0;i<data.length;i++){ const v=(data[i]-128)/128; sum+=v*v }
      const rms = Math.sqrt(sum/data.length)
      const mouth = Math.min(1, rms*6)
      lerpMorph('jawOpen', mouth, 0.5)
    }
  })

  // Since models are not available, render a placeholder
  return (
    <group {...props} ref={group} dispose={null}>
      {/* Placeholder avatar when models are missing */}
      <mesh position={[0, 1, 0]}>
        <boxGeometry args={[0.5, 1, 0.3]} />
        <meshStandardMaterial color="#ff6b6b" />
      </mesh>
      {/* Head placeholder */}
      <mesh position={[0, 1.7, 0]}>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial color="#ffb3b3" />
      </mesh>
      {/* Eyes */}
      <mesh position={[-0.1, 1.75, 0.15]}>
        <sphereGeometry args={[0.03]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
      <mesh position={[0.1, 1.75, 0.15]}>
        <sphereGeometry args={[0.03]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
      {/* Simple mouth */}
      <mesh position={[0, 1.65, 0.15]}>
        <boxGeometry args={[0.08, 0.02, 0.01]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
    </group>
  )
}

// Preload models when they become available
// useGLTF.preload('/models/avatar.glb')
// useGLTF.preload('/models/animations.glb')