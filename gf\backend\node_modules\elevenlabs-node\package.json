{"name": "elevenlabs-node", "version": "1.2.0", "description": "This is an open source Eleven Labs NodeJS package for converting text to speech using the Eleven Labs API", "main": "index.js", "scripts": {"test": "jest --testPathPattern=test --detectOpenHandles --forceExit"}, "repository": {"type": "git", "url": "git+https://github.com/FelixWaweru/elevenlabs-node.git"}, "keywords": ["api", "eleven-labs", "text", "to", "speech", "ai", "voice"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/FelixWaweru/elevenlabs-node/issues"}, "homepage": "https://github.com/FelixWaweru/elevenlabs-node#readme", "dependencies": {"axios": "^1.4.0", "fs-extra": "^11.1.1"}, "devDependencies": {"eslint": "^8.45.0", "jest": "^29.6.1"}}