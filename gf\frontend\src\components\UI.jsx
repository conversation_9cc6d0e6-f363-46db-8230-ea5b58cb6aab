import { useRef, useEffect, useState } from "react";
import { useChat } from "../hooks/useChat";

export const UI = ({ hidden, ...props }) => {
  const input = useRef();
  const { chat, loading, cameraZoomed, setCameraZoomed, message, error, rateLimited, clearError } = useChat();
  const [showError, setShowError] = useState(false);

  const sendMessage = () => {
    const text = input.current.value.trim();
    if (!loading && !message && text) {
      chat(text);
      input.current.value = "";
    }
  };

  useEffect(() => {
    if (error) {
      setShowError(true);
      if (error.isTemporary) {
        const timer = setTimeout(() => {
          setShowError(false);
          clearError();
        }, 5000);
        return () => clearTimeout(timer);
      }
    }
  }, [error, clearError]);

  const handleCloseError = () => {
    setShowError(false);
    clearError();
  };

  if (hidden) return null;

  return (
    <>
      <div className="fixed top-0 left-0 right-0 bottom-0 z-10 flex justify-between p-4 flex-col pointer-events-none">
        <div className="self-start backdrop-blur-md bg-white bg-opacity-50 p-4 rounded-lg">
          <h1 className="font-black text-xl">My Virtual GF</h1>
          <p>I will always love you ❤️</p>
        </div>

        {showError && error && (
          <div className="self-center max-w-md mx-auto pointer-events-auto">
            <div className={`p-4 rounded-lg backdrop-blur-md border-l-4 ${
              error.type === 'quota' || error.type === 'rate_limit_server' || error.type === 'rate_limit_client'
                ? 'bg-yellow-100 bg-opacity-90 border-yellow-500 text-yellow-800'
                : error.type === 'network_error'
                ? 'bg-red-100 bg-opacity-90 border-red-500 text-red-800'
                : 'bg-blue-100 bg-opacity-90 border-blue-500 text-blue-800'
            }`}>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {error.type === 'quota' || error.type === 'rate_limit_server' || error.type === 'rate_limit_client' ? (
                    <svg className="h-5 w-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  ) : error.type === 'network_error' ? (
                    <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium">{error.message}</p>
                  {error.retryAfter && (
                    <p className="text-xs mt-1 opacity-75">Please try again in {error.retryAfter < 60 ? `${error.retryAfter} seconds` : `${Math.ceil(error.retryAfter / 60)} minutes`}</p>
                  )}
                </div>
                <div className="ml-auto pl-3">
                  <button onClick={handleCloseError} className="inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 focus:outline-none">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="w-full flex flex-col items-end justify-center gap-4">
          <button onClick={() => setCameraZoomed(!cameraZoomed)} className="pointer-events-auto bg-pink-500 hover:bg-pink-600 text-white p-4 rounded-md">
            {cameraZoomed ? (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607zM13.5 10.5h-6" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607zM10.5 7.5v6m3-3h-6" />
              </svg>
            )}
          </button>
          <button onClick={() => { const body = document.querySelector("body"); body.classList.toggle('greenScreen') }} className="pointer-events-auto bg-pink-500 hover:bg-pink-600 text-white p-4 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
            </svg>
          </button>
        </div>

        <div className="flex items-center gap-2 pointer-events-auto max-w-screen-sm w-full mx-auto">
          <input className={`w-full placeholder:text-gray-800 placeholder:italic p-4 rounded-md bg-opacity-50 backdrop-blur-md ${ rateLimited ? 'bg-yellow-100 border-2 border-yellow-400' : 'bg-white' }`} placeholder={rateLimited ? "Please wait before sending..." : "Type a message..."} ref={input} disabled={rateLimited}
            onKeyDown={(e) => { if (e.key === "Enter" && !rateLimited) sendMessage(); }} />
          <button disabled={loading || message || rateLimited} onClick={sendMessage} className={`p-4 px-10 font-semibold uppercase rounded-md transition-colors ${ loading || message || rateLimited ? "cursor-not-allowed opacity-30 bg-gray-400" : "bg-pink-500 hover:bg-pink-600 text-white" }`}>
            {loading ? "..." : rateLimited ? "Wait" : "Send"}
          </button>
        </div>
      </div>
    </>
  );
};