![Video Thumbnail](https://img.youtube.com/vi/EzzcEL_1o9o/maxresdefault.jpg)

[Video tutorial](https://youtu.be/EzzcEL_1o9o)

The backend is [here](https://github.com/wass08/r3f-virtual-girlfriend-backend).

## Setup

### 1. Install Dependencies
```bash
yarn
```

### 2. Add 3D Model Files
The application requires 3D model files to display the virtual character:

1. Create or obtain `avatar.glb` and `animations.glb` files
2. Place them in `public/models/` directory
3. See `public/models/README.md` for detailed instructions

**Note**: The app includes fallback rendering, so it will work without models (showing a placeholder instead).

### 3. Start Development Server
```bash
yarn dev
```

## Troubleshooting

- **404 errors for model files**: Check that GLB files are in `public/models/`
- **Avatar not appearing**: Models may be missing - see fallback placeholder
- **Console errors**: Check browser console for specific model loading issues
